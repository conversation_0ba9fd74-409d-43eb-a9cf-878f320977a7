#====================================================================================================
#
#    @file        compiler_option.mak
#    @version     1.1.0
#
#    @brief       Make file used for storing compile and link options
#    @details     This file contains compiler specific options for building the system

#
#====================================================================================================

#\/----------------------------------------------------- GREEN HILLS START -----------------------------------------------------\/

## Toolchain executables ##
CC          := $(TOOLCHAIN_DIR)/ccarm
AS          := $(TOOLCHAIN_DIR)/ccarm
AR          := $(TOOLCHAIN_DIR)/ccarm
LD          := $(TOOLCHAIN_DIR)/cxarm
OC          := $(TOOLCHAIN_DIR)/gmemfile
OC1         := $(TOOLCHAIN_DIR)/gsrec
OD          := $(TOOLCHAIN_DIR)/gdump
CCSIZE      := $(TOOLCHAIN_DIR)/gsize
NM          := $(TOOLCHAIN_DIR)/gnm
OBJCOPY     := $(TOOLCHAIN_DIR)/gmemfile

CC_OPTS         := -e _start -align8 -nostartfiles -no_misalign_pack -Wall -G --gnu_asm \
                   -glimits -dwarf2 -cpu=cortexm7 -memory --short_enum \
                   -fdata-sections -ffunction-sections -lnk=-delete -lnk=-v -preprocess_assembly_files

CC_OPTS_MCAL    := -DAUTOSAR_OS_NOT_USED -DS32G3 -DS32G3XX -DUSE_PORT_HLD -DS32XX

CFLAGS          := $(CC_OPTS)
ASFLAGS         := $(CC_OPTS)
LDFLAGS         := $(CC_OPTS)

CFLAGS          += $(CC_OPTS_MCAL)
ASFLAGS         += $(CC_OPTS_MCAL)

ARFLAGS         := -archive \
                   -o

#/\----------------------------------------------------- GREEN HILLS END -----------------------------------------------------/\
