/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvAdc_Cfg.h                                                                                         */
/* ================================================================================================================= */
/* ================================================================================================================= */
#ifndef CDD_IOHWDRVADC_CFG_H
#define CDD_IOHWDRVADC_CFG_H

/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Std_Types.h"	/* AUTOSAR standard types */

/* ================================================================================================================= */
/*                                               Defines & Macros                                                    */
/* ================================================================================================================= */
/*  */
#define CDD_IOHWDRVADC_INIT_CHECK_API           (STD_OFF)
/*  */
#define CDD_IOHWDRVADC_DEM_EVENT_REPORT         (STD_OFF)

/* IoHwDrv_AdcGetOrginalVal Api */
#define CDD_IOHWDRVADC_GET_ORIGINALVAL_API      (STD_ON)
/* IoHwDrv_AdcGetVoltageVal Api */
#define CDD_IOHWDRVADC_GET_VOLTAGEVAL_API       (STD_ON)

#define CDD_IOHWDRVADC_CHANNEL_NUM              (2U)

/* Brief Define the VAREF vlaue,according the hardware structure to set */
#define CDD_IOHWDRVADC_VOLT_REFERENCE                    (3300U) //unit: Mv
/* Brief coversion the voltage unit from Mv to V */
#define CDD_IOHWDRVADC_VOLT_MVTOV                        (1000.0f)

/* ================================================================================================================= */
/*                                                Public Types                                                       */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                          Public Variables Declarations                                            */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                           Public Functions Declarations                                           */
/* ================================================================================================================= */


#endif /* CDD_IOHWDRVADC_CFG_H */

