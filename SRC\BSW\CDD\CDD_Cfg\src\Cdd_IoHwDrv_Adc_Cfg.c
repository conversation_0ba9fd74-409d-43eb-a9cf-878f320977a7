/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvAdc_Cfg.c                                                                                         */
/* ================================================================================================================= */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Cdd_IoHwDrv_Adc_Cfg.h"
#include "Cdd_IoHwDrv_Adc.h"

/* ================================================================================================================= */
/*                                               Public Variables                                                    */
/* ================================================================================================================= */

const Adc_GroupType Cdd_IoHwDrvAdc_AdcGroupId0 = AdcConf_AdcGroup_AdcGroup_0;

const Adc_GroupType Cdd_IoHwDrvAdc_AdcGroupId1 = AdcConf_AdcGroup_AdcGroup_1;

const IoHwDrv_AdcChConfigType IoHwDrv_AdcChannelConfigTab[CDD_IOHWDRVADC_CHANNEL_NUM] = 
{
    {0, 0, 0, FALSE, FilterNotUsed, 1000, 0, 1500, 0, 0.25},
    {1, 1, 0, FALSE, FilterNotUsed, 1000, 0, 1500, 0, 0.25}
};

#if (CDD_IOHWDRVICU_DEM_EVENT_REPORT == STD_ON)
const Dem_EventIdType Cdd_IoHwDrvAdc_DemEventList[CDD_IOHWDRVADC_CHANNEL_NUM] = 
{
    CDD_IOHWDRV_DEM_EVENT_ID_SKIP,
	  CDD_IOHWDRV_DEM_EVENT_ID_SKIP
};
#endif

