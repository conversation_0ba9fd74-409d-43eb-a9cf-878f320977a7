/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrv.h                                                                                             */
/* ================================================================================================================= */
/* ================================================================================================================= */
#ifndef CDD_IOHWDRV_H
#define CDD_IOHWDRV_H

/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                               Defines & Macros                                                    */
/* ================================================================================================================= */
/* Version Information */
#define CDD_IOHWDRV_MAJOR_VERSION          (1u)
#define CDD_IOHWDRV_MINOR_VERSION          (0u)
#define CDD_IOHWDRV_PATCH_VERSION          (0u)

#define CDD_IOHWDRV_DEM_EVENT_ID_SKIP               (0u)

#define CDD_IOHWDRV_EVENT_DEFINE(event)             DemConf_DemEventParameter_##event

#define CDD_IOHWDRV_MODULE_ID                       (0x320U)
#define CDD_IOHWDRV_VENDORID                        (0x01U)
#define CDD_IOHWDRV_INSTANCE_ID                     (1u)
#define CDD_IOHWDRV_INSTANCE_ID_DET                 (CDD_IOHWDRV_INSTANCE_ID)

#define SchM_Enter_IoHwDrv_SyncLock                  SuspendAllInterrupts
#define SchM_Exit_IoHwDrv_SyncLock                   ResumeAllInterrupts


/* ================================================================================================================= */
/*                                                Public Types                                                       */
/* ================================================================================================================= */
typedef uint16 Cdd_IoHwDrv_AdcChType;
typedef uint16 Cdd_IoHwDrv_PwmChType;
typedef uint16 Cdd_IoHwDrv_IcuChType;
typedef uint16 Cdd_IoHwDrv_GpioChType;

typedef enum
{
	CDD_IOHWDRV_UINIT 	= 0x00,
	CDD_IOHWDRV_INIT 	= 0x01,
}Cdd_IoHwDrv_InitStatusType;

typedef enum
{
    FilterNotUsed = 0U,
	MedianFiltering,
	MeanFiltering
}IoHwDrv_AdcFilterType;

typedef struct 
{
    uint8 ChannelId;
    uint8 Map_AdcGrpId;
    uint8 Map_AdcGrpDefIdx;
    boolean AdcFilterEn;
    IoHwDrv_AdcFilterType AdcFilter;
    uint16 AdcChHighLimit;
    uint16 AdcChLowLimit;
    uint16 VoltageAdcChHighLimit;
    uint16 VoltageAdcChLowLimit;
    float Original2VoltageParameter;
}IoHwDrv_AdcChConfigType;

typedef struct 
{
    uint8 ChannelId;
    uint8 Map_PwmChId;
}IoHwDrv_PwmChConfigType;

typedef struct 
{
    uint8 ChannelId;
    uint8 Map_IcuChId;
}IoHwDrv_IcuChConfigType;

typedef struct 
{
    uint8 ChannelId;
    uint8 Map_DioChId;
    boolean GpioDebounceEn;
    uint16 GpioDebounceTime;
}IoHwDrv_GpioChConfigType;

typedef struct 
{
    uint32 ActiveTime;
    uint32 PeriodTime;
}IoHwDrv_IcuDutyCycleType;

/* ================================================================================================================= */
/*                                          Public Variables Declarations                                            */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                           Public Functions Declarations                                           */
/* ================================================================================================================= */

extern void CDD_IoHwDrv_Init(void);

#endif /* CDD_IOHWDRV_H */

