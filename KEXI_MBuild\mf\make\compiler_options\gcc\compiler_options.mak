#====================================================================================================
#
#    @file        compiler_option.mak
#    @version     1.1.0
#
#    @brief       Make file used for storing compile and link options
#    @details     This file contains compiler specific options for building the system

#
#====================================================================================================

#\/----------------------------------------------------- GCC START -----------------------------------------------------\/

## Toolchain executables ##
CC       := $(TOOLCHAIN_DIR)/bin/arm-none-eabi-gcc.exe
AS       := $(TOOLCHAIN_DIR)/bin/arm-none-eabi-gcc.exe
AR       := $(TOOLCHAIN_DIR)/bin/arm-none-eabi-ar.exe
LD       := $(TOOLCHAIN_DIR)/bin/arm-none-eabi-ld.exe

CFLAGS   := -g \
            -O3 \
            -mcpu=cortex-m7 \
            -mthumb \
            -std=c99 \
            -Os \
            -mfpu=fpv5-sp-d16 \
            -mfloat-abi=hard \
            -c \
            -ggdb3 \
            -mlittle-endian \
            -Wall \
            -Wextra \
            -Wstrict-prototypes \
            -Wunused \
            -Werror=implicit-function-declaration \
            -Wsign-compare \
            -Wundef \
            -Wdouble-promotion \
            -funsigned-char \
            -funsigned-bitfields \
            -fstack-usage \
            -fdump-ipa-all \
            -fno-common \
            -pedantic \
            -I"include" \
            -specs=nano.specs \
            -specs=nosys.specs \
            --sysroot="$(TOOLCHAIN_DIR)/arm-none-eabi/newlib"

ASFLAGS  := -g \
            -c \
            -x assembler-with-cpp \
            -mcpu=cortex-m7 \
            -mthumb \
            -mfpu=fpv5-sp-d16 \
            -mfloat-abi=hard

ARFLAGS  := rc

LDFLAGS  := -mcpu=cortex-m7 \
            -nostartfiles \
            -Wl,-Map=$(BIN_DIR)/$(PRJ_NAME)_$(PLATFORM).map \
            -lm \
            -lgcc \
            -lc \
            -nostartfiles \
            -mthumb \
            -ggdb3 \
            -mlittle-endian \
            -specs=nano.specs \
            -specs=nosys.specs \
            --sysroot="$(TOOLCHAIN_DIR)/arm-none-eabi/newlib" \
            -mfpu=fpv5-sp-d16 -mfloat-abi=hard

#/\----------------------------------------------------- GCC END -----------------------------------------------------/\

