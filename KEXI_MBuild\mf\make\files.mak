#====================================================================================================
#
#    @file        files.mak
#    @version     1.1.0
#
#    @brief       files to be build for the current application.
#    @details     List of files to be built for the target.
#
#====================================================================================================

## Define SOURCEDIR variable ##
BASEDIR :=../..
SOURCEDIR := $(BASEDIR)/SRC
SOURCEDIR_HSW := $(SOURCEDIR)/HSW
SOURCEDIR_BSW := $(SOURCEDIR)/BSW
SOURCEDIR_ASW := $(SOURCEDIR)/ASW
SOURCEDIR_BMSW := $(SOURCEDIR)/BMSW
SOURCEDIR_AMSW := $(SOURCEDIR)/AMSW
LIBS_PATH      := $(SOURCEDIR)/LIB
SOURCEDIR_CDD := $(SOURCEDIR)/BSW/CDD
SOURCEDIR_CCORE := $(SOURCEDIR)/BSW/cCore
SOURCEDIR_MAIN := $(SOURCEDIR)/MAIN

## Including all subdir.mak files below ##
include  $(SOURCEDIR_ASW)/subdir.mak
include  $(SOURCEDIR_BSW)/subdir.mak
include  $(SOURCEDIR_HSW)/subdir.mak
include  $(SOURCEDIR_MAIN)/make.mak

## Path to the linker definition file ##
LD_EXT := ld
LINKER_DEFS ?= $(SOURCEDIR_HSW)/MCAL_Cfg/linker/$(TOOLCHAIN)/linker_$(PLATFORM).$(LD_EXT)
