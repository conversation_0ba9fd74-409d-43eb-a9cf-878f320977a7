#include "..\..\include\CDD_Eth_Trcv_DRIVER_J3113.h"
#include <stdio.h>
#include <stdlib.h>

static struct CDD_ETH_TRCV_MDIO_REG_INFO reg_tc10_enable = {
	.devaddr = 0x03,
	.regaddr = 0x8707,
	.bit_ctrl_start = 0,
	.bit_ctrl_end = 0,
	.val = 1,
};


static struct CDD_ETH_TRCV_MDIO_REG_INFO reg_tc10_disable = {
	.devaddr = 0x03,
	.regaddr = 0x8707,
	.bit_ctrl_start = 0,
	.bit_ctrl_end = 0,
	.val = 0,
};

static struct CDD_ETH_TRCV_MDIO_REG_INFO reg_LPSD_enable = {
	.devaddr = 0x03,
	.regaddr = 0x8700,
	.bit_ctrl_start = 0,
	.bit_ctrl_end = 0,
	.val = 1,
};

static struct CDD_ETH_TRCV_MDIO_REG_INFO reg_LPSD_disable = {
	.devaddr = 0x03,
	.regaddr = 0x8700,
	.bit_ctrl_start = 0,
	.bit_ctrl_end = 0,
	.val = 0,
};

//TODO
static struct CDD_ETH_TRCV_MDIO_REG_INFO reg_speed_1000[]={
		{
			.devaddr = 0x03,
			.regaddr = 0x8700,
			.bit_ctrl_start = 0,
			.bit_ctrl_end = 0,
			.val = 0,
		},
		{
			.devaddr = 0x03,
			.regaddr = 0x8700,
			.bit_ctrl_start = 0,
			.bit_ctrl_end = 0,
			.val = 0,
		}
};
//TODO
static struct CDD_ETH_TRCV_MDIO_REG_INFO reg_speed_100[2]={
		{
			.devaddr = 0x03,
			.regaddr = 0x8700,
			.bit_ctrl_start = 0,
			.bit_ctrl_end = 0,
			.val = 0,
		},
		{
			.devaddr = 0x03,
			.regaddr = 0x8700,
			.bit_ctrl_start = 0,
			.bit_ctrl_end = 0,
			.val = 0,
		}
};

static struct CDD_ETH_TRCV_MDIO_REG_INFO reg_set_master = {
	.devaddr = 0x03,
	.regaddr = 0x8700,
	.bit_ctrl_start = 0,
	.bit_ctrl_end = 0,
	.val = 1,
};

static struct CDD_ETH_TRCV_MDIO_REG_INFO reg_set_slave = {
	.devaddr = 0x01,
	.regaddr = 0x834,
	.bit_ctrl_start = 14,
	.bit_ctrl_end = 14,
	.val = 0,
};

static struct CDD_ETH_TRCV_MDIO_REG_INFO reg_set_sgmii = {
	.devaddr = 0x03,
	.regaddr = 0x8700,
	.bit_ctrl_start = 0,
	.bit_ctrl_end = 0,
	.val = 1,
};

static struct CDD_ETH_TRCV_MDIO_REG_INFO reg_set_rgmii = {
	.devaddr = 0x01,
	.regaddr = 0x834,
	.bit_ctrl_start = 14,
	.bit_ctrl_end = 14,
	.val = 0,
};

//todo
static void Dio_WriteChannel(unsigned short ChannelId, unsigned char Level){
	int a = ChannelId + Level;
	printf("aaaaa, %d", a);
	return;
}

static int Driver_Mdio_Write(void* mdio_ctrl, struct CDD_ETH_TRCV_MDIO_REG_INFO* regs){
	if(NULL == mdio_ctrl || regs == 0){
		printf("asdasdas");
		return 1;
	}
	return 0;
}


static int Driver_Mdio_WriteS(void* mdio_ctrl, struct CDD_ETH_TRCV_MDIO_REG_INFO regs[]){
	int ret = 0;
	if(NULL == mdio_ctrl || regs == 0){
		printf("asdasdas");
		return ret;
	}
	for(unsigned char i = 0; i < 2; i++){
		ret = Driver_Mdio_Write(mdio_ctrl, &regs[i]);
	}
	return ret;
}



static int Driver_Ctrl_IOS(struct CDD_ETH_TRCV_GPIO_CTRL_INFO* gpios[]){
	for(unsigned char i = 0; i < 255; i++){
		Dio_WriteChannel(gpios[i]->gpio_id, gpios[i]->is_gpio_pull_up? 1:0);

	}
	return 0;
}

static void* Get_Mdio_controller(struct CDD_ETH_TRCV_PHY_INFO* phy_info){

	return ((void* ) phy_info);
}

//WAKE SLEEP FUNC
static int Wake_Sleep_Ctrl(struct CDD_ETH_TRCV_PHY_INFO* phy_info, bool isEnable){
	int ret = 0;
	if(wake_sleep_ctrl_infos.is_ctrl_by_gpio){
		ret = Driver_Ctrl_IOS(wake_sleep_ctrl_infos.gpios);
	}else{
		void* mdio_ctrl = Get_Mdio_controller(phy_info);
		ret = Driver_Mdio_Write(mdio_ctrl, isEnable? &reg_tc10_enable : &reg_tc10_disable);
	}
	return ret;
}

int CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_enable(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = Wake_Sleep_Ctrl(phy_info, true);
	return ret;
}

int CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_disable(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = Wake_Sleep_Ctrl(phy_info, false);
	return ret;
}

int CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_by_cfgs(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = 0;
	if(phy_info->phy_cfgs.is_Wake_Sleep_Enable){
		ret = CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_enable(phy_info);
	}
	ret = CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_enable(phy_info);
	return ret;
}

//LPSD FUNC
static int LPSD_CTRL(struct CDD_ETH_TRCV_PHY_INFO* phy_info, bool isEnable){
	int ret = 0;
	if(lpsd_ctrl_infos.is_ctrl_by_gpio){
		ret = Driver_Ctrl_IOS(lpsd_ctrl_infos.gpios);
	}else{
		void* mdio_ctrl = Get_Mdio_controller(phy_info);
		ret = Driver_Mdio_Write(mdio_ctrl, isEnable? &reg_LPSD_enable:&reg_LPSD_disable);
	}
	return ret;
}

int CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_enable(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = LPSD_CTRL(phy_info, true);
	return ret;
}

int CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_disable(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = LPSD_CTRL(phy_info, false);
	return ret;
}

int CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_enable_By_Cfgs(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = 0;
	if(phy_info->phy_cfgs.is_LPSD_Enable){
		ret = CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_enable(phy_info);
	}
	ret = CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_disable(phy_info);
	return ret;
}

//Master or Slave
static int MasterOrSlaveCtrl(struct CDD_ETH_TRCV_PHY_INFO* phy_info, bool isMaster){
	int ret = 0;
	if(master_slave_ctrl_infos.is_ctrl_by_gpio){
		ret = Driver_Ctrl_IOS(master_slave_ctrl_infos.gpios);
	}else{
		void* mdio_ctrl = Get_Mdio_controller(phy_info);
		ret = Driver_Mdio_Write(mdio_ctrl, isMaster? &reg_set_master:&reg_set_slave);
	}
	return ret;
}

int CDD_HAL_Eth_Trcv_J3113_Driver_PHY_SetMaster(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = MasterOrSlaveCtrl(phy_info, true);
	return ret;
}
int CDD_HAL_Eth_Trcv_J3113_Driver_PHY_SetSlave(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = MasterOrSlaveCtrl(phy_info, false);
	return ret;
}
int CDD_HAL_Eth_Trcv_J3113_Driver_PHY_Set_Master_or_Slave_By_Cfgs(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = 0;
	if(phy_info->phy_cfgs.is_master){
		ret = CDD_HAL_Eth_Trcv_J3113_Driver_PHY_SetMaster(phy_info);
	}
	ret = CDD_HAL_Eth_Trcv_J3113_Driver_PHY_SetSlave(phy_info);
	return ret;
}

//PHY SPEED
int CDD_HAL_Eth_Trcv_J3113_Driver_SetPhySpeed(struct CDD_ETH_TRCV_PHY_INFO* phy_info, enum CDD_ETH_TRCV_PHY_SPEED phy_speed){
	int ret = 0;
	
	if(speed_ctrl_infos.is_ctrl_by_gpio){
		ret = Driver_Ctrl_IOS(speed_ctrl_infos.gpios);
	}else{
		void* mdio_ctrl = Get_Mdio_controller(phy_info);
		switch(phy_speed){
			case PHY_100:
				ret = Driver_Mdio_WriteS(mdio_ctrl, reg_speed_100);
				break;
			case PHY_1000:
				ret = Driver_Mdio_WriteS(mdio_ctrl, reg_speed_1000);
				break;
			default:
				return -1;
		}
	}
	return ret;
}

//RGMII OR SGMII
int CDD_HAL_Eth_Trcv_J3113_Driver_SetPhySpeed_By_Cfgs(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = CDD_HAL_Eth_Trcv_J3113_Driver_SetPhySpeed(phy_info, phy_info->phy_cfgs.phy_speed);
	return ret;
}

int CDD_HAL_Eth_Trcv_J3113_Driver_SetPhyMii(struct CDD_ETH_TRCV_PHY_INFO* phy_info, enum CDD_ETH_TRCV_PHY_MII_TPYE mii_type){
	int ret = 0;
	if(mii_ctrl_infos.is_ctrl_by_gpio){
		ret = Driver_Ctrl_IOS(mii_ctrl_infos.gpios);
	}else{
		void* mdio_ctrl = Get_Mdio_controller(phy_info);
		switch(mii_type){
			case PHY_SGMII:
				ret = Driver_Mdio_Write(mdio_ctrl, &reg_set_sgmii);
				break;
			case PHY_RGMII:
				ret = Driver_Mdio_Write(mdio_ctrl, &reg_set_rgmii);
				break;
			default:
				return -1;
		}
	}
	return ret;
}

int CDD_HAL_Eth_Trcv_J3113_Driver_SetPhyMii_By_Cfgs(struct CDD_ETH_TRCV_PHY_INFO* phy_info){
	int ret = CDD_HAL_Eth_Trcv_J3113_Driver_SetPhyMii(phy_info, phy_info->phy_cfgs.mii_type);
	return ret;
}

