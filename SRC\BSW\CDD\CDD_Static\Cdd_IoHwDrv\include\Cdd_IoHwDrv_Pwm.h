/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvAdc.h                                                                                             */
/* ================================================================================================================= */
/* ================================================================================================================= */
#ifndef CDD_IOHWDRV_PWM_H
#define CDD_IOHWDRV_PWM_H


/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Pwm.h"
#include "Cdd_IoHwDrv_Pwm_Cfg.h"
#include "Cdd_IoHwDrv.h"

#if (CDD_IOHWDRVADC_DEM_EVENT_REPORT == STD_ON)
#include "Dem.h" 
#endif
#if (CDD_IOHWDRV_DET_REPORTERROR == STD_ON)
#include "Det.h" 
#endif

/* ================================================================================================================= */
/*                                               Defines & Macros                                                    */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Public Types                                                       */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                          Public Variables Declarations                                            */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                           Public Functions Declarations                                           */
/* ================================================================================================================= */

extern void IoHwDrv_PwmSetPeriod( Cdd_IoHwDrv_PwmChType channelId, uint8 period);
extern void IoHwDrvPwmSetDutyCycle( Cdd_IoHwDrv_PwmChType channelId, uint8 dutyCycle);
extern void IoHwDrv_PwmSetPeriodAndDuty( Cdd_IoHwDrv_PwmChType channelId,uint8 period,uint8 dutyCycle);

#endif /* CDD_IOHWDRV_PWM_H */

