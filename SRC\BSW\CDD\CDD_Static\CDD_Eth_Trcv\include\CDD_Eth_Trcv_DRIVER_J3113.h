
#ifndef INCLUDE_CDD_DRIVER_J3113_H_
#define INCLUDE_CDD_DRIVER_J3113_H_
#include ".\CDD_Eth_Trcv.h"
#include ".\CDD_Eth_Trcv_Base_Type.h"
#include "..\..\..\CDD_Cfg\inc\CDD_Eth_Trcv_DRIVER_J3113_Cfg.h"

int CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_enable(struct CDD_ETH_TRCV_PHY_INFO* phy_info);
int CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_disable(struct CDD_ETH_TRCV_PHY_INFO* phy_info);
int CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_by_cfgs(struct CDD_ETH_TRCV_PHY_INFO* phy_info);

int CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_enable(struct CDD_ETH_TRCV_PHY_INFO* phy_info);
int CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_disable(struct CDD_ETH_TRCV_PHY_INFO* phy_info);
int CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_enable_By_Cfgs(struct CDD_ETH_TRCV_PHY_INFO* phy_info);

int CDD_HAL_Eth_Trcv_J3113_Driver_PHY_SetMaster(struct CDD_ETH_TRCV_PHY_INFO* phy_info);
int CDD_HAL_Eth_Trcv_J3113_Driver_PHY_SetSlave(struct CDD_ETH_TRCV_PHY_INFO* phy_info);
int CDD_HAL_Eth_Trcv_J3113_Driver_PHY_Set_Master_or_Slave_By_Cfgs(struct CDD_ETH_TRCV_PHY_INFO* phy_info);

int CDD_HAL_Eth_Trcv_J3113_Driver_SetPhySpeed(struct CDD_ETH_TRCV_PHY_INFO* phy_info, enum CDD_ETH_TRCV_PHY_SPEED phy_speed);
int CDD_HAL_Eth_Trcv_J3113_Driver_SetPhySpeed_By_Cfgs(struct CDD_ETH_TRCV_PHY_INFO* phy_info);

int CDD_HAL_Eth_Trcv_J3113_Driver_SetPhyMii(struct CDD_ETH_TRCV_PHY_INFO* phy_info, enum CDD_ETH_TRCV_PHY_MII_TPYE mii_type);
int CDD_HAL_Eth_Trcv_J3113_Driver_SetPhyMii_By_Cfgs(struct CDD_ETH_TRCV_PHY_INFO* phy_info);
#endif

