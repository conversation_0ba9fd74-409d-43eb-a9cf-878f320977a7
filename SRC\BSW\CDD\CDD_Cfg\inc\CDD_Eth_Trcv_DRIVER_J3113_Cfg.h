/*
 * driver_cfg.h
 *
 *  Created on: 2025年7月12日
 *      Author: nesibu3
 */

//动态代码部分，当前为样例文件

#ifndef CDD_ETH_TRCX_DRIVER_J3113_CFG_H_
#define CDD_ETH_TRCX_DRIVER_J3113_CFG_H_

#include "..\..\CDD_Static\CDD_Eth_Trcv\include\CDD_Eth_Trcv_Base_Type.h"



//to generate
//对应功能勾选gpio控制后，创建如下相应代码
#define CDD_ETH_TRCV_WAKE_SLEEP_CTRL_GPIO true          //Wakeup_Sleep_Enable   勾选后  IS_IO_CTRL 勾选 生成这条
#define CDD_ETH_TRCV_LPSD_CTRL_GPIO true                //LPSDEnable            勾选后  IS_IO_CTRL 勾选 生成这条
#define CDD_ETH_TRCV_PHY_SPEED_CTRL_GPIO true           //ETH_PHY_Speed         选好后  IS_IO_CTRL 勾选 生成这条
#define CDD_ETH_TRCV_MII_CTRL_GPIO true                 //MII_TYPE              选好后  IS_IO_CTRL 勾选 生成这条
#define CDD_ETH_TRCV_MASTER_OR_SLAVE_CTRL_GPIO true     //Master_Or_Slave       选好后  IS_IO_CTRL 勾选 生成这条


#ifdef CDD_ETH_TRCV_WAKE_SLEEP_CTRL_GPIO                    //固定生成的
    #define CDD_ETH_TRCV_WAKE_SLEEP_CTRL_GPIO_NUMS 2        //根据此条配置了多少gpio，生成相应参数
#endif                                                      //固定生成的

#ifdef CDD_ETH_TRCV_LPSD_CTRL_GPIO                          //固定生成的
    #define CDD_ETH_TRCV_LPSD_CTRL_GPIO_GPIO_NUMS 2         //根据此条配置了多少gpio，生成相应参数
#endif                                                      //固定生成的

#ifdef CDD_ETH_TRCV_PHY_SPEED_CTRL_GPIO                     //固定生成的
    #define CDD_ETH_TRCV_PHY_SPEED_CTRL_GPIO_NUMS 2         //根据此条配置了多少gpio，生成相应参数
#endif                                                      //固定生成的

#ifdef CDD_ETH_TRCV_MII_CTRL_GPIO                           //固定生成的
    #define CDD_ETH_TRCV_MII_CTRL_GPIO_NUMS 2               //根据此条配置了多少gpio，生成相应参数
#endif                                                      //固定生成的

#ifdef CDD_ETH_TRCV_MASTER_OR_SLAVE_CTRL_GPIO               //固定生成的
    #define CDD_ETH_TRCV_MASTER_OR_SLAVE_CTRL_GPIO_NUMS 2   //根据此条配置了多少gpio，生成相应参数
#endif                                                      //固定生成的



//to generate
extern struct CDD_ETH_TRCV_FUNC_CTRL wake_sleep_ctrl_infos;         //固定生成的
extern struct CDD_ETH_TRCV_FUNC_CTRL lpsd_ctrl_infos;               //固定生成的
extern struct CDD_ETH_TRCV_FUNC_CTRL speed_ctrl_infos;              //固定生成的
extern struct CDD_ETH_TRCV_FUNC_CTRL mii_ctrl_infos;                //固定生成的
extern struct CDD_ETH_TRCV_FUNC_CTRL master_slave_ctrl_infos;       //固定生成的

#endif /* DRIVER_DRIVER_CFG_H_ */
