/*
 * base_type.h
 *
 *  Created on: 2025年7月11日
 *      Author: nesibu3
 */

#ifndef INCLUDE_BASE_TYPE_H_
#define INCLUDE_BASE_TYPE_H_
#include <stdbool.h>

#define MAX_CTRL_GPIOS 10
#define MAX_CTRL_REGS 10

// ECU类型
enum CDD_ETH_TRCV_ECU_PLATFORM_TYPE {
    S32G399A
};

//PHY芯片类型
enum CDD_ETH_TRCV_PHY_TYPE{
	J3113,
	J3114,
	J3115
};

enum CDD_ETH_TRCV_PHY_SPEED{
	PHY_100,
	PHY_1000
};

enum CDD_ETH_TRCV_PHY_MII_TPYE{
	PHY_MII,
	PHY_SGMII,
	PHY_RGMII
};

struct CDD_ETH_TRCV_PHY_CFGS
{
	bool is_Wake_Sleep_Enable;
	bool is_LPSD_Enable;
	enum CDD_ETH_TRCV_PHY_SPEED phy_speed;
	bool is_master;
	enum CDD_ETH_TRCV_PHY_MII_TPYE mii_type;
};

//PHY芯片通过IO功能控制,IO数据结构
struct CDD_ETH_TRCV_GPIO_CTRL_INFO
{
    unsigned short gpio_id;
    bool is_gpio_pull_up;
};

//PHY芯片通过寄存器控制,控制器上MDIO的物理引脚接线位置
struct CDD_ETH_TRCV_MDIO_CTRL_INFO
{
    unsigned short mdio_id;
    unsigned short mdc_id;
};

struct CDD_ETH_TRCV_MDIO_REG_INFO{
	unsigned short devaddr;
	unsigned short regaddr;
	unsigned short bit_ctrl_start;
	unsigned short bit_ctrl_end;
	unsigned short val;
};

//PHY数据结构
struct CDD_ETH_TRCV_PHY_INFO
{
	unsigned short index;
	enum CDD_ETH_TRCV_ECU_PLATFORM_TYPE ecu_type;
	enum CDD_ETH_TRCV_PHY_TYPE phy_type;
	struct CDD_ETH_TRCV_PHY_CFGS phy_cfgs;
	struct CDD_ETH_TRCV_MDIO_CTRL_INFO mdio_ctrl_info;
};

//控制方式描述数据结构
//当is_ctrl_by_gpio == true时，操作io
struct CDD_ETH_TRCV_FUNC_CTRL{
	bool is_ctrl_by_gpio;
	struct CDD_ETH_TRCV_GPIO_CTRL_INFO* gpios[MAX_CTRL_GPIOS];
	struct CDD_ETH_TRCV_MDIO_REG_INFO* regs[MAX_CTRL_REGS];
};


#endif /* INCLUDE_BASE_TYPE_H_ */
