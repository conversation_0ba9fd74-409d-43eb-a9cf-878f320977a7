#ifndef INCLUDE_CDD_ETH_TRCV_H_
#define INCLUDE_CDD_ETH_TRCV_H_

#include <stdbool.h>
#include ".\CDD_Eth_Trcv_Base_Type.h"
#include ".\CDD_Eth_Trcv_Hal.h"

int CDD_Eth_Trcv_Wake_Sleep_enable(unsigned char phy_idex);
int CDD_Eth_Trcv_Wake_Sleep_disable(unsigned char phy_idex);
int CDD_ETH_Trcv_Wake_Sleep_enable_By_Cfgs(unsigned char phy_idex);

int CDD_Eth_Trcv_LPSD_enable(unsigned char phy_idex);
int CDD_Eth_Trcv_LPSD_disable(unsigned char phy_idex);
int CDD_ETH_Trcv_LPSD_enable_By_Cfgs(unsigned char phy_idex);

int CDD_Eth_Trcv_PHY_SetMaster(unsigned char phy_idex);
int CDD_Eth_Trcv_PHY_SetSlave(unsigned char phy_idex);
int CDD_ETH_Trcv_PHY_set_Master_or_Slave_By_Cfgs(unsigned char phy_idex);

int CDD_Eth_Trcv_SetPhySpeed(unsigned char phy_idex, enum CDD_ETH_TRCV_PHY_SPEED phy_speed);
int CDD_Eth_Trcv_SetPhySpeed_By_Cfgs(unsigned char phy_idex);

int CDD_Eth_Trcv_SetPhyMii(unsigned char phy_idex, enum CDD_ETH_TRCV_PHY_MII_TPYE mii_type);
int CDD_Eth_Trcv_SetPhyMii_By_Cfgs(unsigned char phy_idex);

#endif
