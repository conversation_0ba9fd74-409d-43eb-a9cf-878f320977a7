#include "..\inc\CDD_Eth_Trcv_DRIVER_J3113_Cfg.h"

//wake_sleep_ctrl_info
#ifdef CDD_ETH_TRCV_WAKE_SLEEP_CTRL_GPIO
	struct CDD_ETH_TRCV_GPIO_CTRL_INFO wake_sleep_ctrl_gpios[CDD_ETH_TRCV_WAKE_SLEEP_CTRL_GPIO_NUMS] = {
		{1,true},				//如果配置了GPIO，根据GPIO配置情况，动态生成的，每条是一个GPIO信息，1是IO_ID，true代表pull_up,0代表pull_down
		{0,true}				//如果配置了GPIO，根据GPIO配置情况，动态生成的，每条是一个GPIO信息，1是IO_ID，true代表pull_up,0代表pull_down
	};
	struct CDD_ETH_TRCV_FUNC_CTRL wake_sleep_ctrl_infos = {
		true, wake_sleep_ctrl_gpios
	};
#elif
	struct CDD_ETH_TRCV_FUNC_CTRL tc10_ctrl_infos = {
		false, null
	}
#endif

//LPSD
#ifdef CDD_ETH_TRCV_LPSD_CTRL_GPIO
	struct CDD_ETH_TRCV_GPIO_CTRL_INFO lpsd_ctrl_gpios[CDD_ETH_TRCV_LPSD_CTRL_GPIO_GPIO_NUMS] = {
		{1,true}				///如果配置了GPIO，根据GPIO配置情况，动态生成的，每条是一个GPIO信息，1是IO_ID，true代表pull_up,0代表pull_down
	};
	struct CDD_ETH_TRCV_FUNC_CTRL lpsd_ctrl_infos = {
		true, lpsd_ctrl_gpios,
	};
#elif
	struct CDD_ETH_TRCV_FUNC_CTRL lpsd_ctrl_infos = {
		false, null
	}
#endif


//SPEED_CTRL
#ifdef CDD_ETH_TRCV_PHY_SPEED_CTRL_GPIO
	struct CDD_ETH_TRCV_GPIO_CTRL_INFO speed_ctrl_gpios[CDD_ETH_TRCV_PHY_SPEED_CTRL_GPIO_NUMS] = {
		{1,true}				//如果配置了GPIO，根据GPIO配置情况，动态生成的，每条是一个GPIO信息，1是IO_ID，true代表pull_up,0代表pull_down
	};
	struct CDD_ETH_TRCV_FUNC_CTRL speed_ctrl_infos = {
		true, speed_ctrl_gpios
	};
#elif
	struct CDD_ETH_TRCV_FUNC_CTRL speed_ctrl_infos = {
		false, null
	}
#endif

//S_R_GMII_CTRL
#ifdef CDD_ETH_TRCV_MII_CTRL_GPIO
	struct CDD_ETH_TRCV_GPIO_CTRL_INFO mii_ctrl_gpios[CDD_ETH_TRCV_MII_CTRL_GPIO_NUMS] = {
		{1,true}				//如果配置了GPIO，根据GPIO配置情况，动态生成的，每条是一个GPIO信息，1是IO_ID，true代表pull_up,0代表pull_down
	};
	struct CDD_ETH_TRCV_FUNC_CTRL mii_ctrl_infos = {
		true,mii_ctrl_gpios
	};
#elif
	struct CDD_ETH_TRCV_FUNC_CTRL mii_ctrl_infos = {
	    false, null
	}
#endif

//master slave
#ifdef CDD_ETH_TRCV_MASTER_OR_SLAVE_CTRL_GPIO
	struct CDD_ETH_TRCV_GPIO_CTRL_INFO master_slave_ctrl_gpios[CDD_ETH_TRCV_MASTER_OR_SLAVE_CTRL_GPIO_NUMS] = {
		{1,true}				//如果配置了GPIO，根据GPIO配置情况，动态生成的，每条是一个GPIO信息，1是IO_ID，true代表pull_up,0代表pull_down
	};
	struct CDD_ETH_TRCV_FUNC_CTRL master_slave_ctrl_infos = {
		true,master_slave_ctrl_gpios
	};
#elif
	struct CDD_ETH_TRCV_FUNC_CTRL master_slave_ctrl_infos = {
		false,null
	};
#endif