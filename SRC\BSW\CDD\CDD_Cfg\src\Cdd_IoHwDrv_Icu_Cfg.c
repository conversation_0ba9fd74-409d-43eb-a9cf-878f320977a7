/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvIcu_Cfg.c                                                                                         */
/* ================================================================================================================= */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Cdd_IoHwDrv_Icu_Cfg.h"
#include "Cdd_IoHwDrv_Icu.h"

/* ================================================================================================================= */
/*                                               Public Variables                                                    */
/* ================================================================================================================= */

const IoHwDrv_IcuChConfigType IoHwDrv_IcuChannelConfigTab[CDD_IOHWDRVICU_CHANNEL_NUM] = 
{
    {0, 0},
    {1, 1}
};

#if (CDD_IOHWDRVICU_DEM_EVENT_REPORT == STD_ON)
const Dem_EventIdType Cdd_IoHwDrvIcu_DemEventList[CDD_IOHWDRVICU_CHANNEL_NUM] = 
{
      CDD_IOHWDRV_DEM_EVENT_ID_SKIP,
	  CDD_IOHWDRV_DEM_EVENT_ID_SKIP
};
#endif

