

#include "CDD_Eth_Trcv_Base_Type.h"
#include "..\include\CDD_Eth_Trcv.h"
#include "..\include\CDD_Eth_Trcv_Hal.h"
#include <stdio.h>

int CDD_Eth_Trcv_Wake_Sleep_enable(unsigned char phy_idex){
	int ret = CDD_HAL_Eth_Trcv_Wake_Sleep_enable(phy_idex);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}
int CDD_Eth_Trcv_Wake_Sleep_disable(unsigned char phy_idex){
	int ret = CDD_HAL_Eth_Trcv_Wake_Sleep_disable(phy_idex);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}
int CDD_ETH_Trcv_Wake_Sleep_enable_By_Cfgs(unsigned char phy_idex){
	int ret = CDD_HAL_ETH_Trcv_Wake_Sleep_enable_By_Cfgs(phy_idex);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}

int CDD_Eth_Trcv_LPSD_enable(unsigned char phy_idex){
	int ret = CDD_HAL_Eth_Trcv_LPSD_enable(phy_idex);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}
int CDD_Eth_Trcv_LPSD_disable(unsigned char phy_idex){
	int ret = CDD_HAL_Eth_Trcv_LPSD_disable(phy_idex);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}
int CDD_ETH_Trcv_LPSD_enable_By_Cfgs(unsigned char phy_idex){
	int ret = CDD_HAL_ETH_Trcv_LPSD_enable_By_Cfgs(phy_idex);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}

int CDD_Eth_Trcv_PHY_SetMaster(unsigned char phy_idex){
	int ret = CDD_HAL_Eth_Trcv_PHY_SetMaster(phy_idex);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}
int CDD_Eth_Trcv_PHY_SetSlave(unsigned char phy_idex){
	int ret = CDD_HAL_Eth_Trcv_PHY_SetSlave(phy_idex);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}
int CDD_ETH_Trcv_PHY_set_Master_or_Slave_By_Cfgs(unsigned char phy_idex){
//	int ret = CDD_HAL_ETH_Trcv_PHY_set_Master_or_Slave_By_Cfgs(phy_idex);
//	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return 0;
}

int CDD_Eth_Trcv_SetPhySpeed(unsigned char phy_idex, enum CDD_ETH_TRCV_PHY_SPEED phy_speed){
	int ret = CDD_HAL_Eth_Trcv_SetPhySpeed(phy_idex, phy_speed);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}

int CDD_Eth_Trcv_SetPhySpeed_By_Cfgs(unsigned char phy_idex){
	int ret = CDD_HAL_Eth_Trcv_SetPhySpeed_By_Cfgs(phy_idex);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}

int CDD_Eth_Trcv_SetPhyMii(unsigned char phy_idex, enum CDD_ETH_TRCV_PHY_MII_TPYE mii_type){
	int ret = CDD_HAL_Eth_Trcv_SetPhyMii(phy_idex, mii_type);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}
int CDD_Eth_Trcv_SetPhyMii_By_Cfgs(unsigned char phy_idex){
	int ret = CDD_HAL_Eth_Trcv_SetPhyMii_By_Cfgs(phy_idex);
	if(ret < 0){printf("ERROR CALL IN %s",__func__);}
	return ret;
}

