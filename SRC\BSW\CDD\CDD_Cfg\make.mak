#=========================================================================================================================
#   @file       CDD_Cfg.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/

SRC_DIRS__ := $(SOURCEDIR_BSW)/CDD/CDD_Cfg/src
INCLUDE_DIRS__ := $(SOURCEDIR_BSW)/CDD/CDD_Cfg/inc

FILES__ := $(wildcard $(SOURCEDIR_BSW)/CDD/CDD_Cfg/src/*.c)


SRC_TARGET_WITHOUT_PATH__ := $(notdir $(FILES__))
OBJS__ := $(SRC_TARGET_WITHOUT_PATH__:%.c=$(OBJ_DIR)/%.o)

## Add source and include directories to global variable
SRC_DIRS += $(SRC_DIRS__)
INCLUDE_DIRS += $(INCLUDE_DIRS__)

## Add files and objs to global variable
FILES_HSW_MCAL_CFG += $(FILES__)
OBJS_HSW_MCAL_CFG += $(OBJS__)