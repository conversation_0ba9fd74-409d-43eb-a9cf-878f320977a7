/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvAdc_Cfg.h                                                                                         */
/* ================================================================================================================= */
/* ================================================================================================================= */
#ifndef CDD_IOHWDRVPWM_CFG_H
#define CDD_IOHWDRVPWM_CFG_H

/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Std_Types.h"	/* AUTOSAR standard types */

/* ================================================================================================================= */
/*                                               Defines & Macros                                                    */
/* ================================================================================================================= */
/*  */
#define CDD_IOHWDRVADC_INIT_CHECK_API           (STD_OFF)
/*  */
#define CDD_IOHWDRVICU_DEM_EVENT_REPORT         (STD_OFF)

/* IoHwDrv_PwmSetDutyCycle Api */
#define CDD_IOHWDRV_PWMSETDUTYCYCLE_API         (STD_ON)
/* IoHwDrv_PwmSetPeriod Api */
#define CDD_IOHWDRV_PWMSETPERIOD_API            (STD_ON)
/* IoHwDrv_PwmSetPeriodAndDuty Api */
#define CDD_IOHWDRV_PWMSETPERIODANDDUTY_API     (STD_ON)

#define CDD_IOHWDRVPWM_CHANNEL_NUM              (1U)


/* ================================================================================================================= */
/*                                                Public Types                                                       */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                          Public Variables Declarations                                            */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                           Public Functions Declarations                                           */
/* ================================================================================================================= */


#endif /* CDD_IOHWDRVPWM_CFG_H */

