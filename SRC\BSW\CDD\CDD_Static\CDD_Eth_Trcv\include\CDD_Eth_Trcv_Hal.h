/*
 * CDD_RTH_PHY_HAL.H
 *
 *  Created on: 2025年7月11日
 *      Author: nesibu3
 */

#ifndef CDD_HAL_ETH_TRCV_H_
#define CDD_HAL_ETH_TRCV_H_
#include ".\CDD_Eth_Trcv_DRIVER_J3113.h"
#include "..\..\..\CDD_Cfg\inc\CDD_Eth_Trcv_Hal_Cfg.h"
#include ".\CDD_Eth_Trcv_Base_Type.h"


int CDD_HAL_Eth_Trcv_Wake_Sleep_enable(unsigned char phy_idex);
int CDD_HAL_Eth_Trcv_Wake_Sleep_disable(unsigned char phy_idex);
int CDD_HAL_ETH_Trcv_Wake_Sleep_enable_By_Cfgs(unsigned char phy_idex);

int CDD_HAL_Eth_Trcv_LPSD_enable(unsigned char phy_idex);
int CDD_HAL_Eth_Trcv_LPSD_disable(unsigned char phy_idex);
int CDD_HAL_ETH_Trcv_LPSD_enable_By_Cfgs(unsigned char phy_idex);

int CDD_HAL_Eth_Trcv_PHY_SetMaster(unsigned char phy_idex);
int CDD_HAL_Eth_Trcv_PHY_SetSlave(unsigned char phy_idex);
int CDD_HAL_ETH_Trcv_PHY_Set_Master_or_Slave_By_Cfgs(unsigned char phy_idexo);

int CDD_HAL_Eth_Trcv_SetPhySpeed(unsigned char phy_idex, enum CDD_ETH_TRCV_PHY_SPEED phy_speed);
int CDD_HAL_Eth_Trcv_SetPhySpeed_By_Cfgs(unsigned char phy_idex);

int CDD_HAL_Eth_Trcv_SetPhyMii(unsigned char phy_idex, enum CDD_ETH_TRCV_PHY_MII_TPYE mii_type);
int CDD_HAL_Eth_Trcv_SetPhyMii_By_Cfgs(unsigned char phy_idexo);


#endif /* CDD_RTH_PHY_HAL_H_ */
