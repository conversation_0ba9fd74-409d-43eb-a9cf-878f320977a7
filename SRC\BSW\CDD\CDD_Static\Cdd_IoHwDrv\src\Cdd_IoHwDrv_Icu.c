/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvAdc.c                                                                                             */
/* ================================================================================================================= */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Cdd_IoHwDrv_Icu.h"

#if (CDD_IOHWDRV_DEV_ERROR_DETECT == STD_ON)
#include "Det.h"
#endif

/* ================================================================================================================= */
/*                                               Defines & Macros                                                    */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Private Types                                                      */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                               Public Variables                                                    */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                               Private Variables                                                   */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                         Private Functions Prototypes                                              */
/* ================================================================================================================= */
#if (CDD_IOHWDRVADC_DEM_EVENT_REPORT == STD_ON)
static void Cdd_IoHwDrvAdc_ReportDemError(const Dem_EventIdType EventId,const Dem_EventStatusType EventStatus);
#endif

/* ================================================================================================================= */
/*                                            Public Functions Definitions                                           */
/* ================================================================================================================= */

/**********************************************************************************************************************
  Functionname:   IoHwDrv_IcuStartSignalMeasurement                                 

  Description:    Get Adc voltage value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
void IoHwDrv_IcuStartSignalMeasurement
( 
    Cdd_IoHwDrv_IcuChType channelId 
)
{

}

/**********************************************************************************************************************
  Functionname:   IoHwDrv_IcuStopSignalMeasurement                                 

  Description:    Get Adc voltage value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
void IoHwDrv_IcuStopSignalMeasurement
( 
    Cdd_IoHwDrv_IcuChType channelId 
)
{

}

/**********************************************************************************************************************
  Functionname:   IoHwDrv_IcuEnableEdgeDetection                                 

  Description:    Get Adc voltage value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
void IoHwDrv_IcuEnableEdgeDetection
( 
    Cdd_IoHwDrv_IcuChType channelId 
)
{

}

/**********************************************************************************************************************
  Functionname:   IoHwDrv_IcuDisableEdgeDetection                                 

  Description:    Get Adc voltage value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
void IoHwDrv_IcuDisableEdgeDetection
( 
    Cdd_IoHwDrv_IcuChType channelId 
)
{

}

/**********************************************************************************************************************
  Functionname:   IoHwDrv_IcuEnableEdgeCount                                 

  Description:    Get Adc voltage value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
void IoHwDrv_IcuEnableEdgeCount
( 
    Cdd_IoHwDrv_IcuChType channelId 
)
{

}

/**********************************************************************************************************************
  Functionname:   IoHwDrv_IcuDisableEdgeCount                                 

  Description:    Get Adc voltage value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
void IoHwDrv_IcuDisableEdgeCount
( 
    Cdd_IoHwDrv_IcuChType channelId 
)
{

}

/**********************************************************************************************************************
  Functionname:   IoHwDrv_IcuGetDutyCycleValues                                 

  Description:    Get Adc voltage value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
void IoHwDrv_IcuDisableEdgeIoHwDrv_IcuGetDutyCycleValuesCount
( 
    Cdd_IoHwDrv_IcuChType channelId,
    IoHwDrv_IcuDutyCycleType *const DutyCycleVal
)
{

}

/* ================================================================================================================= */
/*                                         Private Functions Definitions                                             */
/* ================================================================================================================= */

#if (CDD_IOHWDRVADC_DEM_EVENT_REPORT == STD_ON)
/**********************************************************************************************************************
  Functionname:   Cdd_IoHwDrvAdc_lReportDemError                                  

  Descriptin:     Search event id and report event to Dem.

  InOutCorrelation:
  param[in]       EventId: Event Id
  param[in]       EventStatus: Event Status
  param[out]      none
  return          void 
**********************************************************************************************************************/
static void Cdd_IoHwDrvAdc_ReportDemError
(
    const Dem_EventIdType EventId,
    const Dem_EventStatusType EventStatus
)
{
    if(CDD_IOHWDRV_DEM_EVENT_ID_SKIP != EventId)
    {
        (void)Dem_SetEventStatus(EventId, EventStatus);
    }
    else
    {
        /* QAC:do nothing */
    }
}
#endif
