/*
 * CDD_HAL_cfgs.c
 *
 *  Created on: 2025年7月14日
 *      Author: nesibu3
 */
#include "..\..\CDD_Static\CDD_Eth_Trcv\include\CDD_Eth_Trcv_Base_Type.h"
#include "..\inc\CDD_Eth_Trcv_Hal_Cfg.h"

struct CDD_ETH_TRCV_PHY_INFO CDD_Eth_Trc_Phy_Infos[CDD_Eth_Trcv_Phy_nums] = {
	{
		0, 						//phy_index
		S32G399A, 				//固定的
		J3113, 					//phy_type	
		{
			true, 				//wake_sleep 勾选后，true 	
			true, 				//lpsd 勾选后，true 
			PHY_100, 			//速率 下拉框   100对应PHY_100，1000对应PHY_1000
			true, 				//master_slave master对应true		
			PHY_SGMII			//Sgmii or Rgmii 下拉框 Sgmii对应PHY_SGMII，Rgmii对应PHY_RGMII，
		}, 
		{
			0, 					//MDIO ID
			0					//MDC ID
		}
	},
		{
		0, 						//phy_index
		S32G399A, 				//固定的
		J3113, 					//phy_type	
		{
			true, 				//wake_sleep 勾选后，true 	
			true, 				//lpsd 勾选后，true 
			PHY_100, 			//速率 下拉框   100对应PHY_100，1000对应PHY_1000
			true, 				//master_slave master对应true		
			PHY_SGMII			//Sgmii or Rgmii 下拉框 Sgmii对应PHY_SGMII，Rgmii对应PHY_RGMII，
		}, 
		{
			0, 					//MDIO ID
			0					//MDC ID
		}
	}
};
