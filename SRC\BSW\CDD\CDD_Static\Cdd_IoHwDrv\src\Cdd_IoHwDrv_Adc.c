/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvAdc.c                                                                                             */
/* ================================================================================================================= */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Cdd_IoHwDrv_Adc.h"

#if (CDD_IOHWDRV_DEV_ERROR_DETECT == STD_ON)
#include "Det.h"
#endif

/* ================================================================================================================= */
/*                                               Defines & Macros                                                    */
/* ================================================================================================================= */
#define CDD_IOHWDRV_SIGN_DATA_U32_REG_DEFAULT          (0x00u)
#define CDD_IOHWDRV_SIGN_DATA_U32_MV_DEFAULT           (0x00u)
#define CDD_IOHWDRV_SIGN_DATA_U32_PHY_DEFAULT          (0x00u)
#define CDD_IOHWDRV_SIGN_DATA_F32_PHY_DEFAULT          (0.0f)

/*convert reg to phy*/
#define CDD_IOHWDRV_CONVERT_DEVIATION(VAL)     ((float32)(((float32)VAL*33000.0f-32760.0f)/3701880.0f))

/* Take the lower 16 bits of the data */
#define TAKE_LOW_16BIT_OF_DATA                       ((uint32)0x0000FFFFu)

/* ================================================================================================================= */
/*                                                Private Types                                                      */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                               Public Variables                                                    */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                               Private Variables                                                   */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                         Private Functions Prototypes                                              */
/* ================================================================================================================= */
#if (CDD_IOHWDRVADC_DEM_EVENT_REPORT == STD_ON)
static void Cdd_IoHwDrvAdc_ReportDemError(const Dem_EventIdType EventId,const Dem_EventStatusType EventStatus);
#endif

/* ================================================================================================================= */
/*                                            Public Functions Definitions                                           */
/* ================================================================================================================= */
#if(  CDD_IOHWDRVADC_GET_ORIGINALVAL_API == STD_ON)
/**********************************************************************************************************************
  Functionname:   IoHwDrv_AdcGetOrginalVal                                 

  Description:    Get Adc original value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
uint16 IoHwDrv_AdcGetOrginalVal
(
    Cdd_IoHwDrv_AdcChType channelId
)
{
    
}
#endif

#if(  CDD_IOHWDRVADC_GET_VOLTAGEVAL_API == STD_ON)
/**********************************************************************************************************************
  Functionname:   IoHwDrv_AdcGetVoltageVal                                 

  Description:    Get Adc voltage value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
uint16 IoHwDrv_AdcGetVoltageVal
(
    Cdd_IoHwDrv_AdcChType channelId
)
{
    
}
#endif

/* ================================================================================================================= */
/*                                         Private Functions Definitions                                             */
/* ================================================================================================================= */

#if (CDD_IOHWDRVADC_DEM_EVENT_REPORT == STD_ON)
/**********************************************************************************************************************
  Functionname:   Cdd_IoHwDrvAdc_lReportDemError                                  

  Descriptin:     Search event id and report event to Dem.

  InOutCorrelation:
  param[in]       EventId: Event Id
  param[in]       EventStatus: Event Status
  param[out]      none
  return          void 
**********************************************************************************************************************/
static void Cdd_IoHwDrvAdc_ReportDemError
(
    const Dem_EventIdType EventId,
    const Dem_EventStatusType EventStatus
)
{
    if(CDD_IOHWDRV_DEM_EVENT_ID_SKIP != EventId)
    {
        (void)Dem_SetEventStatus(EventId, EventStatus);
    }
    else
    {
        /* QAC:do nothing */
    }
}
#endif

