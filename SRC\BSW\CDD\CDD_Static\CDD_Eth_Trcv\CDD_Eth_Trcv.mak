#   @file       Cdd_Eth_Trcv.mak
#   @version    1.0.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/


SRC_DIRS__ := $(SOURCEDIR_BSW)/CDD/CDD_Static/CDD_Eth_Trcv/src
INCLUDE_DIRS__ := $(SOURCEDIR_BSW)/CDD/CDD_Static/CDD_Eth_Trcv/include

FILES__ := $(wildcard $(SOURCEDIR_BSW)/CDD/CDD_Static/CDD_Eth_Trcv/src/*.c)


SRC_TARGET_WITHOUT_PATH__ := $(notdir $(FILES__))
OBJS__ := $(SRC_TARGET_WITHOUT_PATH__:%.c=$(OBJ_DIR)/%.o)

## Add source and include directories to global variable
SRC_DIRS += $(SRC_DIRS__)
INCLUDE_DIRS += $(INCLUDE_DIRS__)

## Add files and objs to global variable
FILES_BSW_CDD_STATIC += $(FILES__)
OBJS_BSW_CDD_STATIC += $(OBJS__)
