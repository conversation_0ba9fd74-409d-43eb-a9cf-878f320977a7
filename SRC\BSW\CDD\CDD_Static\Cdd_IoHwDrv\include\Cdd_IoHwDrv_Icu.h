/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvAdc.h                                                                                             */
/* ================================================================================================================= */
/* ================================================================================================================= */
#ifndef CDD_IOHWDRV_ICU_H
#define CDD_IOHWDRV_ICU_H


/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Icu.h"
#include "Cdd_IoHwDrv_Icu_Cfg.h"
#include "Cdd_IoHwDrv.h"

#if (CDD_IOHWDRVADC_DEM_EVENT_REPORT == STD_ON)
#include "Dem.h" 
#endif
#if (CDD_IOHWDRV_DET_REPORTERROR == STD_ON)
#include "Det.h" 
#endif

/* ================================================================================================================= */
/*                                               Defines & Macros                                                    */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Public Types                                                       */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                          Public Variables Declarations                                            */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                           Public Functions Declarations                                           */
/* ================================================================================================================= */

extern void IoHwDrv_IcuStartSignalMeasurement(Cdd_IoHwDrv_IcuChType channelId);
extern void IoHwDrv_IcuStopSignalMeasurement(Cdd_IoHwDrv_IcuChType channelId);
extern void IoHwDrv_IcuEnableEdgeDetection(Cdd_IoHwDrv_IcuChType channelId);
extern void IoHwDrv_IcuDisableEdgeDetection(Cdd_IoHwDrv_IcuChType channelId);
extern void IoHwDrv_IcuEnableEdgeCount(Cdd_IoHwDrv_IcuChType channelId);
extern void IoHwDrv_IcuDisableEdgeCount(Cdd_IoHwDrv_IcuChType channelId);
extern void IoHwDrv_IcuDisableEdgeIoHwDrv_IcuGetDutyCycleValuesCount(Cdd_IoHwDrv_IcuChType channelId,IoHwDrv_IcuDutyCycleType *const DutyCycleVal);

#endif /* CDD_IOHWDRV_ICU_H */

