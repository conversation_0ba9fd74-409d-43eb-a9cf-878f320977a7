/*
 * CDD_ETH_PHY_HAL.C
 *
 *  Created on: 2025年7月11日
 *      Author: nesibu3
 */

#include <stdio.h>
#include "..\..\include\CDD_Eth_Trcv_Hal.h"
#include "..\..\..\..\CDD_Cfg\inc\CDD_Eth_Trcv_Hal_Cfg.h"
#include "..\..\include\CDD_Eth_Trcv_Base_Type.h"

static struct CDD_ETH_TRCV_PHY_INFO* get_phy_info_by_index(unsigned char phy_idex){

	for(unsigned char i = 0; i < CDD_Eth_Trcv_Phy_nums; i++){
		if(CDD_Eth_Trc_Phy_Infos[i].index == phy_idex){
			return &CDD_Eth_Trc_Phy_Infos[i];
		}
	}
	printf("can not find phy_info!");
	return NULL;
}

int CDD_HAL_Eth_Trcv_Wake_Sleep_enable(unsigned char phy_idex){
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	if(phy_info == NULL){return -1;}
	int ret = 0;
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_enable(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}

int CDD_HAL_Eth_Trcv_Wake_Sleep_disable(unsigned char phy_idex){
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	int ret = 0;
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_disable(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}

int CDD_HAL_ETH_Trcv_Wake_Sleep_enable_By_Cfgs(unsigned char phy_idex){
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	int ret = 0;
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_Wake_Sleep_by_cfgs(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}

int CDD_HAL_Eth_Trcv_LPSD_enable(unsigned char phy_idex){
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	int ret = 0;
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_enable(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}

int CDD_HAL_Eth_Trcv_LPSD_disable(unsigned char phy_idex){
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	int ret = 0;
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_disable(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}

int CDD_HAL_ETH_Trcv_LPSD_enable_By_Cfgs(unsigned char phy_idex){
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	int ret = 0;
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_LPSD_enable_By_Cfgs(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}

int CDD_HAL_Eth_Trcv_PHY_SetMaster(unsigned char phy_idex){
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	int ret = 0;
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_PHY_SetMaster(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}
int CDD_HAL_Eth_Trcv_PHY_SetSlave(unsigned char phy_idex){
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	int ret = 0;
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_PHY_SetSlave(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}
int CDD_HAL_ETH_Trcv_PHY_Set_Master_or_Slave_By_Cfgs(unsigned char phy_idex){
	int ret = 0;
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_PHY_Set_Master_or_Slave_By_Cfgs(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}

int CDD_HAL_Eth_Trcv_SetPhySpeed(unsigned char phy_idex, enum CDD_ETH_TRCV_PHY_SPEED phy_speed){
	int ret = 0;
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_SetPhySpeed(phy_info, phy_speed);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}

int CDD_HAL_Eth_Trcv_SetPhySpeed_By_Cfgs(unsigned char phy_idex){
	int ret = 0;
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_SetPhySpeed_By_Cfgs(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}


int CDD_HAL_Eth_Trcv_SetPhyMii(unsigned char phy_idex, enum CDD_ETH_TRCV_PHY_MII_TPYE mii_type){
	int ret = 0;
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_SetPhyMii(phy_info, mii_type);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}
int CDD_HAL_Eth_Trcv_SetPhyMii_By_Cfgs(unsigned char phy_idex){
	int ret = 0;
	struct CDD_ETH_TRCV_PHY_INFO* phy_info = get_phy_info_by_index(phy_idex);
	if(phy_info == NULL){return -1;}
	switch(phy_info->phy_type){
		case J3113:
			ret = CDD_HAL_Eth_Trcv_J3113_Driver_SetPhyMii_By_Cfgs(phy_info);
			if(ret < 0){printf("SetTc10 FAILED %d",ret); return ret;}
			break;
		default:
			printf("no match eth_phy");
			break;
	}
}



