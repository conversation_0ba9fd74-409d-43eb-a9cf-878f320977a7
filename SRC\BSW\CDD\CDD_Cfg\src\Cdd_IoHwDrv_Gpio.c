/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvGpio_Cfg.c                                                                                         */
/* ================================================================================================================= */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Cdd_IoHwDrv_Gpio_Cfg.h"
#include "Cdd_IoHwDrv_Gpio.h"

/* ================================================================================================================= */
/*                                               Public Variables                                                    */
/* ================================================================================================================= */

const IoHwDrv_GpioChConfigType IoHwDrv_GpioChannelConfigTab[CDD_IOHWDRVGPIO_CHANNEL_NUM] = 
{
    {0, 0x0006U, STD_OFF, 100},
    {1, 0x0007U, STD_OFF, 100}
};
