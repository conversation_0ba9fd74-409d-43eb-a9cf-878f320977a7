/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvAdc.c                                                                                             */
/* ================================================================================================================= */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Cdd_IoHwDrv_Gpio.h"

#if (CDD_IOHWDRV_DEV_ERROR_DETECT == STD_ON)
#include "Det.h"
#endif

/* ================================================================================================================= */
/*                                               Defines & Macros                                                    */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Private Types                                                      */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                               Public Variables                                                    */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                               Private Variables                                                   */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                         Private Functions Prototypes                                              */
/* ================================================================================================================= */
#if (CDD_IOHWDRVGPIO_DEM_EVENT_REPORT == STD_ON)
static void Cdd_IoHwDrvGpio_ReportDemError(const Dem_EventIdType EventId,const Dem_EventStatusType EventStatus);
#endif

/* ================================================================================================================= */
/*                                            Public Functions Definitions                                           */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                         Private Functions Definitions                                             */
/* ================================================================================================================= */

#if (CDD_IOHWDRVGPIO_DEM_EVENT_REPORT == STD_ON)
/**********************************************************************************************************************
  Functionname:   Cdd_IoHwDrvGpio_ReportDemError                                  

  Descriptin:     Search event id and report event to Dem.

  InOutCorrelation:
  param[in]       EventId: Event Id
  param[in]       EventStatus: Event Status
  param[out]      none
  return          void 
**********************************************************************************************************************/
static void Cdd_IoHwDrvGpio_ReportDemError
(
    const Dem_EventIdType EventId,
    const Dem_EventStatusType EventStatus
)
{
    if(CDD_IOHWDRV_DEM_EVENT_ID_SKIP != EventId)
    {
        (void)Dem_SetEventStatus(EventId, EventStatus);
    }
    else
    {
        /* QAC:do nothing */
    }
}
#endif

