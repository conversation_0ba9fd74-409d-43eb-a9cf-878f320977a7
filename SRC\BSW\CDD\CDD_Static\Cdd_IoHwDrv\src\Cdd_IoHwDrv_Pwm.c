/* Module : Cdd_IoHwDrv                                                                                          */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvPwm.c                                                                                             */
/* ================================================================================================================= */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Cdd_IoHwDrv_Pwm.h"

#if (CDD_IOHWDRV_DEV_ERROR_DETECT == STD_ON)
#include "Det.h"
#endif

/* ================================================================================================================= */
/*                                               Defines & Macros                                                    */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Private Types                                                      */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                               Public Variables                                                    */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                               Private Variables                                                   */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                         Private Functions Prototypes                                              */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                            Public Functions Definitions                                           */
/* ================================================================================================================= */

/**********************************************************************************************************************
  Functionname:   IoHwDrv_PwmSetPeriod                                 

  Description:    Get Adc original value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
void IoHwDrv_PwmSetPeriod
( 
    Cdd_IoHwDrv_PwmChType channelId, 
    uint8 period
)
{
    
}

/**********************************************************************************************************************
  Functionname:   IoHwDrvPwmSetDutyCycle                                 

  Description:    Get Adc voltage value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
void IoHwDrvPwmSetDutyCycle
( 
    Cdd_IoHwDrv_PwmChType channelId, 
    uint8 dutyCycle
)
{
    
}

/**********************************************************************************************************************
  Functionname:   IoHwDrv_PwmSetPeriodAndDuty                                 

  Description:    Get Adc voltage value by channel Id.

  InOutCorrelation:
  param[in]       channelId: Channel Id
  param[out]      none
  return          uint16 
**********************************************************************************************************************/
void IoHwDrv_PwmSetPeriodAndDuty
( 
    Cdd_IoHwDrv_PwmChType channelId,
    uint8 period,
    uint8 dutyCycle
)
{

}

/* ================================================================================================================= */
/*                                         Private Functions Definitions                                             */
/* ================================================================================================================= */

#if (Cdd_IoHwDrvPwm_DEM_EVENT_REPORT == STD_ON)
/**********************************************************************************************************************
  Functionname:   Cdd_IoHwDrvPwm_lReportDemError                                  

  Descriptin:     Search event id and report event to Dem.

  InOutCorrelation:
  param[in]       EventId: Event Id
  param[in]       EventStatus: Event Status
  param[out]      none
  return          void 
**********************************************************************************************************************/
static void Cdd_IoHwDrvPwm_ReportDemError
(
    const Dem_EventIdType EventId,
    const Dem_EventStatusType EventStatus
)
{
    if(CDD_IOHWDRV_DEM_EVENT_ID_SKIP != EventId)
    {
        (void)Dem_SetEventStatus(EventId, EventStatus);
    }
    else
    {
        /* QAC:do nothing */
    }
}
#endif

