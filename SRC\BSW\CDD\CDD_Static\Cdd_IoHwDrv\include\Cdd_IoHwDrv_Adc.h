/* Module : Cdd_IoHwDrv                                                                                              */ 
/* Version: V0.1.0                                                                                                   */
/* file Cdd_IoHwDrvAdc.h                                                                                             */
/* ================================================================================================================= */
/* ================================================================================================================= */
#ifndef CDD_IOHWDRV_ADC_H
#define CDD_IOHWDRV_ADC_H


/* ================================================================================================================= */
/*                                                Include Files                                                      */
/* ================================================================================================================= */
#include "Adc.h"
#include "Cdd_IoHwDrv_Adc_Cfg.h"
#include "Cdd_IoHwDrv.h"
// #include "Dma.h"
// #include "IfxSrc_reg.h"

#if (CDD_IOHWDRVADC_DEM_EVENT_REPORT == STD_ON)
#include "Dem.h" 
#endif
#if (CDD_IOHWDRV_DET_REPORTERROR == STD_ON)
#include "Det.h" 
#endif

/* ================================================================================================================= */
/*                                               Defines & Macros                                                    */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                                Public Types                                                       */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                          Public Variables Declarations                                            */
/* ================================================================================================================= */

/* ================================================================================================================= */
/*                                           Public Functions Declarations                                           */
/* ================================================================================================================= */

extern uint16 IoHwDrv_AdcGetOrginalVal(Cdd_IoHwDrv_AdcChType channelId);
extern uint16 IoHwDrv_AdcGetVoltageVal(Cdd_IoHwDrv_AdcChType channelId);

#endif /* CDD_IOHWDRV_ADC_H */

