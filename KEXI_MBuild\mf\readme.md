############################################################################################################
#配置说明：
##	首次使用电脑已正确安装Git 和 Greenhills(ARM版本)
##  构建工具部署, make.exe 解压到电脑本地；
##	把make工具路径添加到电脑系统变量Path中，譬如：C:\MakeTools
##  config.mak 内正确设置目标编译工具链路径，譬如：C:/ghs/comp_202214

#使用说明：
##	进入目录 ..\..\Build\makefile , 右键点击Git Bash here, 输入make命令后回车运行；

#	工程目录说明
├── Project	                               //编译工程
│   ├── KEXI_MBuild		     //主编译目录
│   │   └── mf
│   │       ├── Makefile	                 //主编译makefile
│   │       ├── make		    //子编译make
│   │       ├── objects   	                 //编译产物目录，bin，map，elf， *.o等文件
│   │       └── readme.md
│   ├── SRC
│   │   ├── ASW		     //APP源码放置编译目录
│   │   │   └── APP1        
│   │   │   └── APP2
│   │   │   └── APP3
│   │   │   └── subdir.mak              //子make
│   │   ├── BSW	                   //cdd和cp源码目录
│   │   │   ├── CDD                        //CDD源码
│   │   │   └── cCore                      //CP源码
│   │   ├── HSW		      //mcal代码
│   │   │   ├── MCAL_Cfg               // 可以在这创建子模块目录，包括测试代码目录
│   │   │   └── MCAL_Static 
│   │   ├── LIB		                   // lib库目录
│   │   │   ├── cp_lib
│   │   │   ├── libCDD_BootMgr_ghs.a
│   │   │   ├── libCDD_EcuInfo_ghs.a
│   │   │   ├── libCDD_HwErrDet_ghs.a
│   │   │   └── .......
│   │   └── MAIN
│   │       └── Main.c
│   └── TEST

#	makefile目录说明
mf
│  Makefile					##	Makefile主文件，包含所有组件源码编译功能; mcal_cdd封库功能; mcal_cdd封库后对应源码文件删除功能; 带mcal_cdd库+cp源码编译功能；要求支持多线程编译
│  MakeTools.7z				##  make 工具, 解压后添加到电脑系统变量Path中
│  readme.md				##	编译使用说明文档
│
├─make
│  │   config.mak			##	makefile 配置，按实际项目需求修改
│  │   files.mak			##	编译文件列表，按实际项目需求修改
│  │
│  ├─compiler_options		##	编译选项相关内容，按实际项目需求修改
│  │  ├─gcc
│  │  │      compiler_options.mak
│  │  │      
│  │  ├─ghs
│  │  │      compiler_options.mak
│  │  │      
│  │  └─iar
│  │          compiler_options.mak
└─objects_$(TOOLCHAIN)				##	编译目标文件目录，自动生成
   │  $(PRJ_NAME)_$(ECU)_$(DERIVATIVE).bin         //YL2_S32G3.bin, YL2_YTM32.bin
   │  $(PRJ_NAME)_$(ECU)_$(DERIVATIVE).elf
   │  $(PRJ_NAME)_$(ECU)_$(DERIVATIVE).hex
   │  $(PRJ_NAME)_$(ECU)_$(DERIVATIVE).lst
   │  $(PRJ_NAME)_$(ECU)_$(DERIVATIVE).map
   │  $(PRJ_NAME)_$(ECU)_$(DERIVATIVE).srec
   │  
   └─obj					## 编译中间文件目录
   
		 
