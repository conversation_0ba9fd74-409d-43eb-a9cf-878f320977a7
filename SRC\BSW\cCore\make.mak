#=========================================================================================================================
#   @file       cCore.mak
#   @version    1.2.0
#
#   @brief   This file specifies files under the subdir.mak will be compiled achived and linked
#   @details
#
#===========================================================================================================================*/
FILES_BSW_CCORE :=
OBJS_BSW_CCORE :=

include $(SOURCEDIR_BSW)/cCore/CanIf_TS_T40D11M50I0R0/make.mak
include $(SOURCEDIR_BSW)/cCore/Dem_TS_T40D11M50I0R0/make.mak
include $(SOURCEDIR_BSW)/cCore/Det_TS_T40D11M50I0R0/make.mak
include $(SOURCEDIR_BSW)/cCore/EcuM_TS_T40D11M50I0R0/make.mak
include $(SOURCEDIR_BSW)/cCore/Rte_TS_T40D11M50I0R0/make.mak


## lib name
LIB_BSW_CCORE := $(LIBS_PATH)/lib_BSW_cCORE_$(TOOLCHAIN).a

## Compile rule for lib
$(LIB_BSW_CCORE): $(OBJS_BSW_CCORE)
	@echo [$(TOOLCHAIN)] Archiving $(notdir $@)
	$(AR) $(ARFLAGS) $@ $^

## Add lib files to global variable
FILES_LIB += $(FILES_BSW_CCORE)

## Add obj OR lib to global variable
LIBS += $(LIB_BSW_CCORE)